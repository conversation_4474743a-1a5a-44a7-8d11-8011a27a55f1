<template>
  <div class="login-container">
    <div class="login-wrapper">
      <!-- Logo Section -->
      <div class="logo-section">
        <img src="@/assets/simperum.png" alt="Simperum Logo" class="logo-img" />
        <h1 class="logo-text">Sim<span class="highlight">perum.</span></h1>
        <p class="logo-subtitle">Pintu Gerbang Satu Data Perumahan Jawa Tengah</p>
      </div>

      <!-- Login Card -->
      <Card class="login-card">
        <template #content>
          <div class="login-content">
            <!-- <div class="login-header">
              <h2 class="login-title">Selamat Datang</h2>
              <p class="login-subtitle">Masuk ke akun Anda untuk melanjutkan</p>
            </div> -->

            <form class="login-form" @submit.prevent="handleLogin">
              <!-- Username Input -->
              <div class="form-group">
                <label for="username" class="form-label">Username</label>
                <InputText
                  id="username"
                  v-model="username"
                  type="text"
                  placeholder="Masukkan username Anda"
                  class="w-full"
                  required
                />
              </div>

              <!-- Password Input -->
              <div class="form-group">
                <label for="password" class="form-label">Password</label>
                <Password
                  id="password"
                  v-model="password"
                  placeholder="Masukkan password Anda"
                  class="w-full"
                  toggle-mask
                  required
                />
              </div>

              <!-- Remember Me & Forgot Password -->
              <div class="form-options">
                <!-- <div class="remember-me">
                  <Checkbox v-model="rememberMe" input-id="remember" binary />
                  <label for="remember" class="remember-label">Ingat saya</label>
                </div>
                <a href="#" class="forgot-password">Lupa password?</a> -->
              </div>

              <!-- Login Button -->
              <Button
                type="submit"
                label="Masuk"
                class="login-button"
                :loading="isLoading"
                :disabled="!username || !password"
              />
            </form>

            <!-- Sign Up Link -->
            <!-- <div class="signup-link">
              <span>Belum punya akun? </span>
              <a href="#" class="signup-text">Daftar sekarang</a>
            </div> -->
          </div>
        </template>
      </Card>

      <!-- Back to Home -->
      <Button
        icon="pi pi-arrow-left"
        label="Kembali ke Beranda"
        class="back-button"
        text
        @click="goHome"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import InputText from 'primevue/inputtext'
import Password from 'primevue/password'
import Button from 'primevue/button'
import Card from 'primevue/card'

const router = useRouter()
const authStore = useAuthStore()

// Form data
const username = ref('')
const password = ref('')
const isLoading = ref(false)

// Handle login
const handleLogin = async () => {
  isLoading.value = true

  try {
    const result = await authStore.login(username.value, password.value)

    if (result.success) {
      // Redirect to dashboard on successful login
      router.push('/responsive-layout')
    } else {
      // Show error message
      console.error('Login failed:', result.message)
      // You can add toast notification here if needed
      alert(result.message || 'Login failed. Please check your credentials.')
    }
  } catch (error) {
    console.error('Login error:', error)
    alert('An unexpected error occurred. Please try again.')
  } finally {
    isLoading.value = false
  }
}

// Navigate back to home
const goHome = () => {
  router.push('/')
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #fff 0%, #f3f3f3 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('https://external-content.duckduckgo.com/iu/?u=https%3A%2F%2Fimg.freepik.com%2Fpremium-photo%2Fphoto-modern-home-3d-design_763111-13992.jpg&f=1&nofb=1&ipt=b8464edcb1da416d3970d874bcae853ba9a46483c6707d597d686c787fd47103');
  background-size: cover;
  background-position: center;
  opacity: 0.1;
  z-index: 0;
}

.login-wrapper {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 450px;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.logo-section {
  text-align: center;
  color: #aaa;
  margin-bottom: 1rem;
}

.logo-img {
  height: 60px;
  width: auto;
  margin-bottom: 1rem;
  filter: brightness(0) invert(1);
}

.logo-text {
  font-size: 2.5rem;
  font-weight: 300;
  margin: 0;
  letter-spacing: 0.5px;
}

.highlight {
  font-weight: 500;
  color: #333;
}

.logo-subtitle {
  font-size: 1rem;
  opacity: 0.9;
  margin: 0.5rem 0 0 0;
  font-weight: 300;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.login-content {
  padding: 2rem;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.login-subtitle {
  color: #666;
  margin: 0;
  font-size: 0.95rem;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: -0.5rem;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.remember-label {
  font-size: 0.9rem;
  color: #666;
  cursor: pointer;
}

.forgot-password {
  color: #8bc34a;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
}

.forgot-password:hover {
  text-decoration: underline;
}

.login-button {
  background: linear-gradient(135deg, #8bc34a 0%, #7cb342 100%);
  border: none;
  padding: 0.75rem;
  font-weight: 600;
  font-size: 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.login-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #7cb342 0%, #689f38 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 195, 74, 0.3);
}

.login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.divider-text {
  color: #999;
  font-size: 0.85rem;
  background: white;
  padding: 0 1rem;
}

.social-login {
  margin-top: 1rem;
}

.google-button {
  width: 100%;
  border-color: #ddd;
  color: #333;
  font-weight: 500;
}

.google-button:hover {
  background-color: #f8f9fa;
  border-color: #8bc34a;
}

.signup-link {
  text-align: center;
  margin-top: 1.5rem;
  font-size: 0.9rem;
  color: #666;
}

.signup-text {
  color: #8bc34a;
  text-decoration: none;
  font-weight: 500;
}

.signup-text:hover {
  text-decoration: underline;
}

.back-button {
  align-self: center;
  color: #333;
  font-weight: 500;
}

.back-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-container {
    padding: 1rem;
  }

  .login-wrapper {
    max-width: 100%;
  }

  .login-content {
    padding: 1.5rem;
  }

  .logo-text {
    font-size: 2rem;
  }

  .login-title {
    font-size: 1.5rem;
  }
}

/* PrimeVue Component Overrides */
:deep(.p-inputtext) {
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 0.75rem;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

:deep(.p-inputtext:focus) {
  border-color: #8bc34a;
  box-shadow: 0 0 0 2px rgba(139, 195, 74, 0.2);
}

:deep(.p-password) {
  width: 100%;
}

:deep(.p-password .p-inputtext) {
  border-radius: 8px 0 0 8px;
  width: 100%;
}

:deep(.p-password .p-password-toggle-mask) {
  border-radius: 0 8px 8px 0;
  border-left: none;
  border: 1px solid #e0e0e0;
}

:deep(.p-password .p-password-toggle-mask:hover) {
  border-color: #8bc34a;
}

:deep(.p-checkbox .p-checkbox-box) {
  border-radius: 4px;
  border-color: #e0e0e0;
}

:deep(.p-checkbox .p-checkbox-box.p-highlight) {
  background-color: #8bc34a;
  border-color: #8bc34a;
}

:deep(.p-card .p-card-content) {
  padding: 0;
}

:deep(.p-divider .p-divider-content) {
  background-color: white;
}
</style>
