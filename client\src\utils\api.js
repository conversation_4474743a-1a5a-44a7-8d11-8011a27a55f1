/**
 * API Client Utility
 * Reusable functions for making HTTP requests to the backend
 */
import { md5Hash } from './hash.js'

// Base configuration
const API_BASE_URL = window.location.origin.replace(/8000/, '8001')

/**
 * Default request configuration
 */
const defaultConfig = {
  headers: {
    'Content-Type': 'application/json',
  },
  credentials: 'include', // Include cookies for authentication
}

/**
 * Custom error class for API errors
 */
export class ApiError extends Error {
  constructor(message, status, data = null) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.data = data
  }
}

/**
 * Main API client function
 * @param {string} endpoint - API endpoint (without /api prefix)
 * @param {Object} options - Request options
 * @returns {Promise<Object>} - Response data
 */
export async function apiClient(endpoint, options = {}) {
  const url = `${API_BASE_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`

  const config = {
    ...defaultConfig,
    ...options,
    headers: {
      ...defaultConfig.headers,
      ...options.headers,
    },
  }

  // Add authorization token if available
  const token = getAuthToken()
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }

  try {
    const response = await fetch(url, config)

    // Parse response as JSON
    let data
    try {
      data = await response.json()
    } catch (parseError) {
      throw new ApiError('Invalid JSON response', response.status)
    }

    // Handle non-2xx responses
    if (!response.ok) {
      throw new ApiError(
        data.message || `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        data
      )
    }

    return data
  } catch (error) {
    // Re-throw ApiError as-is
    if (error instanceof ApiError) {
      throw error
    }

    // Handle network errors
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      throw new ApiError('Network error: Unable to connect to server', 0)
    }

    // Handle other errors
    throw new ApiError(error.message || 'Unknown error occurred', 0)
  }
}

/**
 * GET request helper
 * @param {string} endpoint - API endpoint
 * @param {Object} options - Additional request options
 * @returns {Promise<Object>} - Response data
 */
export function apiGet(endpoint, options = {}) {
  return apiClient(endpoint, {
    method: 'GET',
    ...options,
  })
}

/**
 * POST request helper
 * @param {string} endpoint - API endpoint
 * @param {Object} data - Request body data
 * @param {Object} options - Additional request options
 * @returns {Promise<Object>} - Response data
 */
export function apiPost(endpoint, data = null, options = {}) {
  return apiClient(endpoint, {
    method: 'POST',
    body: data ? JSON.stringify(data) : null,
    ...options,
  })
}

/**
 * PUT request helper
 * @param {string} endpoint - API endpoint
 * @param {Object} data - Request body data
 * @param {Object} options - Additional request options
 * @returns {Promise<Object>} - Response data
 */
export function apiPut(endpoint, data = null, options = {}) {
  return apiClient(endpoint, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : null,
    ...options,
  })
}

/**
 * DELETE request helper
 * @param {string} endpoint - API endpoint
 * @param {Object} options - Additional request options
 * @returns {Promise<Object>} - Response data
 */
export function apiDelete(endpoint, options = {}) {
  return apiClient(endpoint, {
    method: 'DELETE',
    ...options,
  })
}

/**
 * Get authentication token from localStorage
 * @returns {string|null} - Auth token or null
 */
function getAuthToken() {
  try {
    return localStorage.getItem('authToken')
  } catch (error) {
    console.warn('Unable to access localStorage:', error)
    return null
  }
}

/**
 * Set authentication token in localStorage
 * @param {string} token - Auth token
 */
export function setAuthToken(token) {
  try {
    if (token) {
      localStorage.setItem('authToken', token)
    } else {
      localStorage.removeItem('authToken')
    }
  } catch (error) {
    console.warn('Unable to access localStorage:', error)
  }
}

/**
 * Login API call
 * @param {string} username - Username
 * @param {string} password - Password
 * @returns {Promise<Object>} - Login response
 */
export async function loginApi(username, password) {
  console.log(username, password)
  const response = await apiPost('/api/login', {
    _Username: username,
    _Password: md5Hash(password),
  })

  // Store token if login successful
  if (response.success && response.token) {
    setAuthToken(response.token)
  }

  return response
}

/**
 * Logout API call (clears local token)
 */
export function logoutApi() {
  setAuthToken(null)
  // Note: Server-side logout would be implemented here if needed
  return Promise.resolve({ success: true })
}
